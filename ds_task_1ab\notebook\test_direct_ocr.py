"""
Test the direct OCR functionality without temp files.
"""

import sys
import os

# Add the parent directory to the path to import services
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.ocr_service import OCR

def test_direct_ocr():
    """
    Test the direct OCR functionality.
    """
    print("=== Direct OCR Test ===")
    
    # Initialize OCR service
    ocr_service = OCR()
    
    print("✓ OCR service created")
    print(f"✓ Tesseract path: {ocr_service.path}")
    
    print("\nOCR Service is ready!")
    print("The service now works directly with uploaded files - no temp files needed!")
    print("\nHow it works:")
    print("1. User uploads image via /ocr-query endpoint")
    print("2. <PERSON><PERSON><PERSON> passes the file object directly to OCR")
    print("3. OCR extracts text using PIL Image.open(file_object)")
    print("4. Text is returned immediately")

if __name__ == "__main__":
    test_direct_ocr()
