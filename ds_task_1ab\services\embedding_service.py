"""
Mock Embedding Service for OCR functionality testing.
This is a placeholder to resolve import dependencies.
"""

import numpy as np
from sentence_transformers import SentenceTransformer

class EmbeddingService:
    """
    Service for generating embeddings from text using sentence transformers.
    """
    
    def __init__(self, model_name='all-MiniLM-L6-v2'):
        """
        Initialize the embedding service.
        
        Args:
            model_name (str): Name of the sentence transformer model to use
        """
        try:
            self.model = SentenceTransformer(model_name)
            self.model_name = model_name
            print(f"Embedding service initialized with model: {model_name}")
        except Exception as e:
            print(f"Warning: Could not load embedding model: {e}")
            self.model = None
            self.model_name = None
    
    def get_embedding(self, text):
        """
        Generate embedding for a single text.
        
        Args:
            text (str): Input text
            
        Returns:
            np.ndarray: Text embedding vector
        """
        if not self.model:
            # Return a dummy embedding if model is not available
            return np.random.rand(384)  # Default dimension for all-MiniLM-L6-v2
        
        try:
            embedding = self.model.encode(text)
            return embedding
        except Exception as e:
            print(f"Error generating embedding: {e}")
            return np.random.rand(384)
    
    def get_embeddings(self, texts):
        """
        Generate embeddings for multiple texts.
        
        Args:
            texts (list): List of input texts
            
        Returns:
            np.ndarray: Array of text embedding vectors
        """
        if not self.model:
            # Return dummy embeddings if model is not available
            return np.random.rand(len(texts), 384)
        
        try:
            embeddings = self.model.encode(texts)
            return embeddings
        except Exception as e:
            print(f"Error generating embeddings: {e}")
            return np.random.rand(len(texts), 384)
    
    def create_embeddings_batch(self, texts, batch_size=32):
        """
        Generate embeddings for texts in batches.

        Args:
            texts (list): List of input texts
            batch_size (int): Batch size for processing

        Returns:
            np.ndarray: Array of text embedding vectors
        """
        if not self.model:
            # Return dummy embeddings if model is not available
            return np.random.rand(len(texts), 384)

        try:
            # Process in batches to avoid memory issues
            all_embeddings = []
            for i in range(0, len(texts), batch_size):
                batch = texts[i:i + batch_size]
                batch_embeddings = self.model.encode(batch)
                all_embeddings.append(batch_embeddings)

            return np.vstack(all_embeddings)
        except Exception as e:
            print(f"Error generating batch embeddings: {e}")
            return np.random.rand(len(texts), 384)

    def is_available(self):
        """
        Check if the embedding service is available.

        Returns:
            bool: True if service is available, False otherwise
        """
        return self.model is not None
