"""
Test script for OCR functionality implementation.
This script demonstrates the OCR service capabilities.
"""

import sys
import os

# Add the parent directory to the path to import services
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.ocr_service import OCRService

def test_ocr_service():
    """
    Test the OCR service functionality.
    """
    print("=== OCR Service Test ===")
    
    # Initialize OCR service
    ocr_service = OCRService()
    
    # Test if Tesseract is available
    print("\n1. Testing Tesseract availability...")
    if ocr_service.is_tesseract_available():
        print("✓ Tesseract is properly installed and accessible")
    else:
        print("✗ Tesseract is not available. Please check installation.")
        return
    
    # Test with a sample image (you can replace this with an actual image path)
    print("\n2. Testing OCR text extraction...")
    
    # Example usage - you would replace this with an actual image path
    sample_image_path = "sample_text_image.png"  # Replace with actual image path
    
    if os.path.exists(sample_image_path):
        print(f"Testing with image: {sample_image_path}")
        
        # Extract text from image
        extracted_text = ocr_service.extract_text_from_image(sample_image_path)
        print(f"Extracted text: '{extracted_text}'")
        
        # Test with preprocessing
        preprocessed_text = ocr_service.preprocess_image(sample_image_path)
        print(f"Preprocessed extracted text: '{preprocessed_text}'")
        
    else:
        print(f"Sample image not found at: {sample_image_path}")
        print("To test OCR functionality:")
        print("1. Place a test image with text in the notebook directory")
        print("2. Update the 'sample_image_path' variable with the correct path")
        print("3. Run this script again")
    
    print("\n=== OCR Service Test Complete ===")

def create_sample_usage_example():
    """
    Show how to use the OCR service in your application.
    """
    print("\n=== Sample Usage Example ===")
    
    example_code = '''
# Example usage of OCR Service:

from services.ocr_service import OCRService

# Initialize the service
ocr = OCRService()

# Method 1: Extract text from image file
text = ocr.extract_text_from_image("path/to/your/image.jpg")
print(f"Extracted text: {text}")

# Method 2: Extract text with preprocessing (better for low quality images)
text = ocr.preprocess_image("path/to/your/image.jpg")
print(f"Preprocessed text: {text}")

# Method 3: Extract text from image data (for web uploads)
with open("image.jpg", "rb") as f:
    image_data = f.read()
text = ocr.extract_text_from_image_data(image_data)
print(f"Text from data: {text}")
'''
    
    print(example_code)

if __name__ == "__main__":
    test_ocr_service()
    create_sample_usage_example()
