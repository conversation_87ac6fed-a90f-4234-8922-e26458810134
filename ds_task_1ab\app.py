from flask import Flask, request, jsonify, render_template
import os
from dotenv import load_dotenv
from services.product_recommendation_service import ProductRecommendationService
from services.ocr_service import OCR


# Load environment variables
load_dotenv()

app = Flask(__name__)

# Initialize services
recommendation_service = None
ocr_service = None

try:
    recommendation_service = ProductRecommendationService()
    print("Product recommendation service initialized successfully")
except Exception as e:
    print(f"Warning: Could not initialize recommendation service: {e}")

try:
    ocr_service = OCR()
    print("OCR service initialized successfully")
except Exception as e:
    print(f"Warning: Could not initialize OCR service: {e}")


@app.route('/product-recommendation', methods=['POST'])
def product_recommendation():
    """
    Endpoint for product recommendations based on natural language queries.
    Input: Form data containing 'query' (string).
    Output: JSON with 'products' (array of objects) and 'response' (string).
    """
    query = request.form.get('query', '')
    
    if not query:
        return jsonify({"error": "Query is required"}), 400
    
    if not recommendation_service:
        return jsonify({"error": "Recommendation service not available"}), 500
    
    try:
        # Get recommendations - let the system return whatever number it finds
        products, response = recommendation_service.get_recommendations(query)
        
        return jsonify({
            "products": products,
            "response": response,
            "query": query,
            "total_found": len(products)
        })
        
    except Exception as e:
        return jsonify({
            "error": "An error occurred while processing your request",
            "products": [],
            "response": "Sorry, I encountered an error while searching for products. Please try again."
        }), 500
    
    
@app.route('/ocr-query', methods=['POST'])
def ocr_query():
    """
    Endpoint to process handwritten queries extracted from uploaded images.
    Input: Form data containing 'image_data' (file, base64-encoded image or direct file upload).
    Output: JSON with 'products' (array of objects) and 'response' (string).
    """
    image_file = request.files.get('image_data')

    if not image_file:
        return jsonify({"error": "Image file is required"}), 400

    if not ocr_service:
        return jsonify({"error": "OCR service not available"}), 500

    if not recommendation_service:
        return jsonify({"error": "Recommendation service not available"}), 500

    try:
        print(f"Processing uploaded image: {image_file.filename}")

        # Extract text from the image using OCR directly
        extracted_text = ocr_service.extract(image_file)

        if not extracted_text or extracted_text == "Error" or not extracted_text.strip():
            return jsonify({
                "error": "No text could be extracted from the image",
                "extracted_text": "",
                "products": [],
                "response": "Sorry, I couldn't extract any readable text from the image. Please try with a clearer image."
            }), 400

        # Use the extracted text to get product recommendations
        products, response = recommendation_service.get_recommendations(extracted_text)

        return jsonify({
            "products": products,
            "response": response,
            "extracted_text": extracted_text,
            "total_found": len(products)
        })

    except Exception as e:
        return jsonify({
            "error": "An error occurred while processing the image",
            "extracted_text": "",
            "products": [],
            "response": "Sorry, I encountered an error while processing your image. Please try again."
        }), 500

@app.route('/image-product-search', methods=['POST'])
def image_product_search():
    """
    Endpoint to identify and suggest products from uploaded product images.
    Input: Form data containing 'product_image' (file, base64-encoded image or direct file upload).
    Output: JSON with 'products' (array of objects) and 'response' (string).
    """
    product_image = request.files.get('product_image')
    # Process the product image to detect and match products
    products = []  # Empty array, to be populated with product data
    response = ""  # Empty string, to be filled with a natural language response
    return jsonify({"products": products, "response": response})




@app.route('/sample_response', methods=['GET'])
def sample_response():
    """
    Endpoint to return a sample JSON response for the API.
    Output: JSON with 'products' (array of objects) and 'response' (string).
    """
    return render_template('sample_response.html')

@app.route('/', methods=['GET'])
def ecommerce_services_page():
    """
    Main page for e-commerce services interface.
    """
    return render_template('ecommerce_services.html')

if __name__ == '__main__':
    app.run(debug=True)
