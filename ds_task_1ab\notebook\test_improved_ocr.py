"""
Test script for improved OCR functionality.
"""

import sys
import os
import cv2
import numpy as np

# Add the parent directory to the path to import services
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.ocr_service import OCRService

def create_test_image():
    """
    Create a simple test image with text for OCR testing.
    """
    # Create a white image
    img = np.ones((200, 600, 3), dtype=np.uint8) * 255
    
    # Add some text
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(img, 'Hello World', (50, 100), font, 2, (0, 0, 0), 3, cv2.LINE_AA)
    cv2.putText(img, 'OCR Test Image', (50, 150), font, 1, (0, 0, 0), 2, cv2.LINE_AA)
    
    # Save the test image
    test_image_path = "test_ocr_image.png"
    cv2.imwrite(test_image_path, img)
    print(f"Created test image: {test_image_path}")
    return test_image_path

def test_improved_ocr():
    """
    Test the improved OCR functionality.
    """
    print("=== Testing Improved OCR Functionality ===")
    
    # Initialize OCR service
    ocr_service = OCRService()
    
    # Check if Tesseract is available
    if not ocr_service.is_tesseract_available():
        print("Tesseract is not available. Cannot proceed with test.")
        return
    
    # Create a test image
    test_image_path = create_test_image()
    
    try:
        print(f"\nTesting with created image: {test_image_path}")
        
        # Test regular extraction
        print("\n1. Testing regular extraction:")
        text1 = ocr_service.extract_text_from_image(test_image_path)
        print(f"   Result: '{text1}'")
        
        # Test preprocessing
        print("\n2. Testing with preprocessing:")
        text2 = ocr_service.preprocess_image(test_image_path)
        print(f"   Result: '{text2}'")
        
        # Test robust extraction
        print("\n3. Testing robust extraction:")
        text3 = ocr_service.extract_text_robust(test_image_path)
        print(f"   Result: '{text3}'")
        
        # Summary
        print(f"\n=== Results Summary ===")
        print(f"Regular extraction: {'✓' if text1.strip() else '✗'}")
        print(f"Preprocessing: {'✓' if text2.strip() else '✗'}")
        print(f"Robust extraction: {'✓' if text3.strip() else '✗'}")
        
        if text3.strip():
            print(f"\n✓ OCR is working correctly!")
            print(f"Best result: '{text3}'")
        else:
            print(f"\n✗ OCR needs further investigation")
        
    finally:
        # Clean up test image
        if os.path.exists(test_image_path):
            os.remove(test_image_path)
            print(f"\nCleaned up test image: {test_image_path}")

def test_with_user_image():
    """
    Test OCR with a user-provided image if available.
    """
    print("\n=== Testing with User Image ===")
    
    # Look for common image files in the current directory
    image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
    found_images = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            found_images.append(file)
    
    if found_images:
        print(f"Found images: {found_images}")
        test_image = found_images[0]
        print(f"Testing with: {test_image}")
        
        ocr_service = OCRService()
        text = ocr_service.extract_text_robust(test_image)
        print(f"Extracted text: '{text}'")
        
        if text.strip():
            print("✓ Successfully extracted text from user image")
        else:
            print("✗ No text extracted from user image")
    else:
        print("No image files found in current directory")
        print("To test with your own image:")
        print("1. Place an image file in the notebook directory")
        print("2. Run this script again")

if __name__ == "__main__":
    test_improved_ocr()
    test_with_user_image()
