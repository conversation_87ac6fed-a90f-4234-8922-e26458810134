from pytesseract import pytesseract
from PIL import Image

class OCR:
    def __init__(self):
        self.path = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

    def extract(self, image_data):
        try:
            pytesseract.tesseract_cmd = self.path
            # If image_data is a file object, convert to PIL Image
            if hasattr(image_data, 'read'):
                image = Image.open(image_data)
            else:
                # If it's a file path
                image = Image.open(image_data)

            text = pytesseract.image_to_string(image)
            return text

        except Exception as e:
            print(e)
            return "Error"
