import pytesseract as pyt
import cv2

class OCRService:
    """
    Simple OCR Service for extracting text from images using Tesseract OCR.
    """

    def __init__(self):
        """
        Initialize the OCR service with Tesseract configuration.
        """
        # Set the path to Tesseract executable
        pyt.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"

    def extract_text_from_image(self, image_path):
        """
        Extract text from an image file using OCR.

        Args:
            image_path (str): Path to the image file

        Returns:
            str: Extracted text from the image
        """
        try:
            # Read the image using OpenCV
            img = cv2.imread(image_path)

            if img is None:
                raise ValueError(f"Could not read image from path: {image_path}")

            # Extract text using pytesseract
            text = pyt.image_to_string(img)
            print(f"Extracted text: '{text}'")

            return text.strip()

        except Exception as e:
            print(f"Error extracting text from image: {str(e)}")
            return ""

    def is_tesseract_available(self):
        """
        Check if Tesseract is properly installed and accessible.

        Returns:
            bool: True if Tesseract is available, False otherwise
        """
        try:
            # Try to get Tesseract version
            version = pyt.get_tesseract_version()
            print(f"Tesseract version: {version}")
            return True
        except Exception as e:
            print(f"Tesseract not available: {str(e)}")
            return False
