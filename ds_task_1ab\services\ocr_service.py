import pytesseract as pyt
import cv2
import os
import io
from PIL import Image
import numpy as np

class OCRService:
    """
    OCR Service for extracting text from images using Tesseract OCR.
    """
    
    def __init__(self):
        """
        Initialize the OCR service with Tesseract configuration.
        """
        # Set the path to Tesseract executable
        pyt.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
    
    def extract_text_from_image(self, image_path):
        """
        Extract text from an image file using OCR.

        Args:
            image_path (str): Path to the image file

        Returns:
            str: Extracted text from the image
        """
        try:
            # Read the image using OpenCV
            img = cv2.imread(image_path)

            if img is None:
                raise ValueError(f"Could not read image from path: {image_path}")

            print(f"Image loaded successfully: {image_path}")
            print(f"Image shape: {img.shape}")

            # Try multiple OCR configurations for better results
            # First try with default settings
            text = pyt.image_to_string(img, config='--psm 6')

            # If no text found, try with different page segmentation modes
            if not text.strip():
                print("Trying with PSM 8 (single word)")
                text = pyt.image_to_string(img, config='--psm 8')

            if not text.strip():
                print("Trying with PSM 7 (single text line)")
                text = pyt.image_to_string(img, config='--psm 7')

            if not text.strip():
                print("Trying with PSM 13 (raw line)")
                text = pyt.image_to_string(img, config='--psm 13')

            print(f"Extracted text: '{text.strip()}'")
            return text.strip()

        except Exception as e:
            print(f"Error extracting text from image: {str(e)}")
            return ""
    
    def extract_text_from_image_data(self, image_data):
        """
        Extract text from image data (bytes or PIL Image).
        
        Args:
            image_data: Image data as bytes or PIL Image object
            
        Returns:
            str: Extracted text from the image
        """
        try:
            # If image_data is bytes, convert to PIL Image
            if isinstance(image_data, bytes):
                image = Image.open(io.BytesIO(image_data))
            elif isinstance(image_data, Image.Image):
                image = image_data
            else:
                raise ValueError("Unsupported image data type")
            
            # Convert PIL Image to numpy array for OpenCV
            img_array = np.array(image)
            
            # Convert RGB to BGR if needed (OpenCV uses BGR)
            if len(img_array.shape) == 3 and img_array.shape[2] == 3:
                img_array = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            
            # Extract text using pytesseract
            text = pyt.image_to_string(img_array)
            
            return text.strip()
            
        except Exception as e:
            print(f"Error extracting text from image data: {str(e)}")
            return ""
    
    def preprocess_image(self, image_path, output_path=None):
        """
        Preprocess image to improve OCR accuracy.

        Args:
            image_path (str): Path to the input image
            output_path (str, optional): Path to save preprocessed image

        Returns:
            str: Extracted text from preprocessed image
        """
        try:
            # Read the image
            img = cv2.imread(image_path)

            if img is None:
                raise ValueError(f"Could not read image from path: {image_path}")

            print(f"Preprocessing image: {image_path}")

            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)

            # Apply threshold to get better contrast
            _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # Apply morphological operations to clean up the image
            kernel = np.ones((1, 1), np.uint8)
            cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

            # Save preprocessed image if output path is provided
            if output_path:
                cv2.imwrite(output_path, cleaned)

            # Try multiple OCR configurations on preprocessed image
            text = pyt.image_to_string(cleaned, config='--psm 6')

            if not text.strip():
                text = pyt.image_to_string(cleaned, config='--psm 8')

            if not text.strip():
                text = pyt.image_to_string(cleaned, config='--psm 7')

            print(f"Preprocessed text: '{text.strip()}'")
            return text.strip()

        except Exception as e:
            print(f"Error preprocessing and extracting text: {str(e)}")
            return ""
    
    def extract_text_robust(self, image_path):
        """
        Robust text extraction that tries multiple methods.

        Args:
            image_path (str): Path to the image file

        Returns:
            str: Extracted text from the image
        """
        print(f"Starting robust text extraction for: {image_path}")

        # Try regular extraction first
        text = self.extract_text_from_image(image_path)
        if text.strip():
            print("Text extracted successfully with regular method")
            return text

        print("Regular extraction failed, trying with preprocessing...")

        # Try with preprocessing
        text = self.preprocess_image(image_path)
        if text.strip():
            print("Text extracted successfully with preprocessing")
            return text

        print("Both methods failed to extract text")
        return ""

    def is_tesseract_available(self):
        """
        Check if Tesseract is properly installed and accessible.

        Returns:
            bool: True if Tesseract is available, False otherwise
        """
        try:
            # Try to get Tesseract version
            version = pyt.get_tesseract_version()
            print(f"Tesseract version: {version}")
            return True
        except Exception as e:
            print(f"Tesseract not available: {str(e)}")
            return False
