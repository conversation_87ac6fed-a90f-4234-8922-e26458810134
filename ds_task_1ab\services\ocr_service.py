from pytesseract import pytesseract
from PIL import Image

class OCR:
    def __init__(self):
        self.path = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

    def extract(self, image_data):
        try:
            pytesseract.tesseract_cmd = self.path
            print(f"OCR: Processing image data type: {type(image_data)}")

            # If image_data is a file object, convert to PIL Image
            if hasattr(image_data, 'read'):
                print("OCR: Reading from file object")
                image_data.seek(0)  # Reset file pointer to beginning
                image = Image.open(image_data)
            else:
                print("OCR: Reading from file path")
                image = Image.open(image_data)

            print(f"OCR: Image loaded successfully, size: {image.size}, mode: {image.mode}")
            text = pytesseract.image_to_string(image)
            print(f"OCR: Raw extracted text: '{text}'")

            # Clean up the text
            cleaned_text = text.strip()
            print(f"OCR: Cleaned text: '{cleaned_text}'")
            return cleaned_text

        except Exception as e:
            print(f"OCR Error: {e}")
            return "Error"
