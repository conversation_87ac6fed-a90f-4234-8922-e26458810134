import pytesseract as pyt
import cv2
import os
import io
from PIL import Image
import numpy as np

class OCRService:
    """
    OCR Service for extracting text from images using Tesseract OCR.
    """
    
    def __init__(self):
        """
        Initialize the OCR service with Tesseract configuration.
        """
        # Set the path to Tesseract executable
        pyt.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
    
    def extract_text_from_image(self, image_path):
        """
        Extract text from an image file using OCR.
        
        Args:
            image_path (str): Path to the image file
            
        Returns:
            str: Extracted text from the image
        """
        try:
            # Read the image using OpenCV
            img = cv2.imread(image_path)
            
            if img is None:
                raise ValueError(f"Could not read image from path: {image_path}")
            
            # Extract text using pytesseract
            text = pyt.image_to_string(img)
            
            return text.strip()
            
        except Exception as e:
            print(f"Error extracting text from image: {str(e)}")
            return ""
    
    def extract_text_from_image_data(self, image_data):
        """
        Extract text from image data (bytes or PIL Image).
        
        Args:
            image_data: Image data as bytes or PIL Image object
            
        Returns:
            str: Extracted text from the image
        """
        try:
            # If image_data is bytes, convert to PIL Image
            if isinstance(image_data, bytes):
                image = Image.open(io.BytesIO(image_data))
            elif isinstance(image_data, Image.Image):
                image = image_data
            else:
                raise ValueError("Unsupported image data type")
            
            # Convert PIL Image to numpy array for OpenCV
            img_array = np.array(image)
            
            # Convert RGB to BGR if needed (OpenCV uses BGR)
            if len(img_array.shape) == 3 and img_array.shape[2] == 3:
                img_array = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            
            # Extract text using pytesseract
            text = pyt.image_to_string(img_array)
            
            return text.strip()
            
        except Exception as e:
            print(f"Error extracting text from image data: {str(e)}")
            return ""
    
    def preprocess_image(self, image_path, output_path=None):
        """
        Preprocess image to improve OCR accuracy.
        
        Args:
            image_path (str): Path to the input image
            output_path (str, optional): Path to save preprocessed image
            
        Returns:
            str: Extracted text from preprocessed image
        """
        try:
            # Read the image
            img = cv2.imread(image_path)
            
            if img is None:
                raise ValueError(f"Could not read image from path: {image_path}")
            
            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Apply threshold to get better contrast
            _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Save preprocessed image if output path is provided
            if output_path:
                cv2.imwrite(output_path, thresh)
            
            # Extract text from preprocessed image
            text = pyt.image_to_string(thresh)
            
            return text.strip()
            
        except Exception as e:
            print(f"Error preprocessing and extracting text: {str(e)}")
            return ""
    
    def is_tesseract_available(self):
        """
        Check if Tesseract is properly installed and accessible.
        
        Returns:
            bool: True if Tesseract is available, False otherwise
        """
        try:
            # Try to get Tesseract version
            version = pyt.get_tesseract_version()
            print(f"Tesseract version: {version}")
            return True
        except Exception as e:
            print(f"Tesseract not available: {str(e)}")
            return False
