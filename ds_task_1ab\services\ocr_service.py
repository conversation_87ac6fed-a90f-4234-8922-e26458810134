from pytesseract import pytesseract
from PIL import Image
import io

class OCR:
    def __init__(self):
        self.path = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

    def extract(self, image_file):
        try:
            pytesseract.tesseract_cmd = self.path

            # Read the file content into bytes
            image_file.seek(0)
            image_bytes = image_file.read()

            # Convert bytes to PIL Image
            image = Image.open(io.BytesIO(image_bytes))

            # Extract text
            text = pytesseract.image_to_string(image)
            return text.strip()

        except Exception as e:
            print(e)
            return "Error"
