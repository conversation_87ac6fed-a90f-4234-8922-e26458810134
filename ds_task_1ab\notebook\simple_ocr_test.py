"""
Simple OCR test following the user's guide.
"""

import sys
import os

# Add the parent directory to the path to import services
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.ocr_service import OCRService

def test_simple_ocr():
    """
    Test the simple OCR functionality.
    """
    print("=== Simple OCR Test ===")
    
    # Initialize OCR service
    ocr_service = OCRService()
    
    # Check if Tesseract is available
    if ocr_service.is_tesseract_available():
        print("✓ Tesseract is working")
    else:
        print("✗ Tesseract is not available")
        return
    
    print("\nOCR Service is ready!")
    print("To test with an image:")
    print("1. Place an image with text in this directory")
    print("2. Update the code below with the image path")
    print("3. Run: text = ocr_service.extract_text_from_image('your_image.jpg')")

if __name__ == "__main__":
    test_simple_ocr()
